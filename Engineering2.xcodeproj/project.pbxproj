// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		E28100B92E3B0686006414B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E281009A2E3B0683006414B0 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E28100A12E3B0683006414B0;
			remoteInfo = Engineering2;
		};
		E28100C32E3B0687006414B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E281009A2E3B0683006414B0 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E28100A12E3B0683006414B0;
			remoteInfo = Engineering2;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		E28100A22E3B0683006414B0 /* Engineering2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Engineering2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E28100B82E3B0686006414B0 /* Engineering2Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Engineering2Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E28100C22E3B0687006414B0 /* Engineering2UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Engineering2UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		E28100CA2E3B0687006414B0 /* Exceptions for "Engineering2" folder in "Engineering2" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = E28100A12E3B0683006414B0 /* Engineering2 */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		E28100A42E3B0683006414B0 /* Engineering2 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E28100CA2E3B0687006414B0 /* Exceptions for "Engineering2" folder in "Engineering2" target */,
			);
			path = Engineering2;
			sourceTree = "<group>";
		};
		E28100BB2E3B0686006414B0 /* Engineering2Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Engineering2Tests;
			sourceTree = "<group>";
		};
		E28100C52E3B0687006414B0 /* Engineering2UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Engineering2UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		E281009F2E3B0683006414B0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E28100B52E3B0686006414B0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E28100BF2E3B0687006414B0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		E28100992E3B0683006414B0 = {
			isa = PBXGroup;
			children = (
				E28100A42E3B0683006414B0 /* Engineering2 */,
				E28100BB2E3B0686006414B0 /* Engineering2Tests */,
				E28100C52E3B0687006414B0 /* Engineering2UITests */,
				E28100A32E3B0683006414B0 /* Products */,
			);
			sourceTree = "<group>";
		};
		E28100A32E3B0683006414B0 /* Products */ = {
			isa = PBXGroup;
			children = (
				E28100A22E3B0683006414B0 /* Engineering2.app */,
				E28100B82E3B0686006414B0 /* Engineering2Tests.xctest */,
				E28100C22E3B0687006414B0 /* Engineering2UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E28100A12E3B0683006414B0 /* Engineering2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E28100CB2E3B0687006414B0 /* Build configuration list for PBXNativeTarget "Engineering2" */;
			buildPhases = (
				E281009E2E3B0683006414B0 /* Sources */,
				E281009F2E3B0683006414B0 /* Frameworks */,
				E28100A02E3B0683006414B0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				E28100A42E3B0683006414B0 /* Engineering2 */,
			);
			name = Engineering2;
			packageProductDependencies = (
			);
			productName = Engineering2;
			productReference = E28100A22E3B0683006414B0 /* Engineering2.app */;
			productType = "com.apple.product-type.application";
		};
		E28100B72E3B0686006414B0 /* Engineering2Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E28100D02E3B0687006414B0 /* Build configuration list for PBXNativeTarget "Engineering2Tests" */;
			buildPhases = (
				E28100B42E3B0686006414B0 /* Sources */,
				E28100B52E3B0686006414B0 /* Frameworks */,
				E28100B62E3B0686006414B0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E28100BA2E3B0686006414B0 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E28100BB2E3B0686006414B0 /* Engineering2Tests */,
			);
			name = Engineering2Tests;
			packageProductDependencies = (
			);
			productName = Engineering2Tests;
			productReference = E28100B82E3B0686006414B0 /* Engineering2Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		E28100C12E3B0687006414B0 /* Engineering2UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E28100D32E3B0687006414B0 /* Build configuration list for PBXNativeTarget "Engineering2UITests" */;
			buildPhases = (
				E28100BE2E3B0687006414B0 /* Sources */,
				E28100BF2E3B0687006414B0 /* Frameworks */,
				E28100C02E3B0687006414B0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E28100C42E3B0687006414B0 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E28100C52E3B0687006414B0 /* Engineering2UITests */,
			);
			name = Engineering2UITests;
			packageProductDependencies = (
			);
			productName = Engineering2UITests;
			productReference = E28100C22E3B0687006414B0 /* Engineering2UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E281009A2E3B0683006414B0 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					E28100A12E3B0683006414B0 = {
						CreatedOnToolsVersion = 16.4;
					};
					E28100B72E3B0686006414B0 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = E28100A12E3B0683006414B0;
					};
					E28100C12E3B0687006414B0 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = E28100A12E3B0683006414B0;
					};
				};
			};
			buildConfigurationList = E281009D2E3B0683006414B0 /* Build configuration list for PBXProject "Engineering2" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E28100992E3B0683006414B0;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = E28100A32E3B0683006414B0 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E28100A12E3B0683006414B0 /* Engineering2 */,
				E28100B72E3B0686006414B0 /* Engineering2Tests */,
				E28100C12E3B0687006414B0 /* Engineering2UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E28100A02E3B0683006414B0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E28100B62E3B0686006414B0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E28100C02E3B0687006414B0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E281009E2E3B0683006414B0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E28100B42E3B0686006414B0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E28100BE2E3B0687006414B0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		E28100BA2E3B0686006414B0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E28100A12E3B0683006414B0 /* Engineering2 */;
			targetProxy = E28100B92E3B0686006414B0 /* PBXContainerItemProxy */;
		};
		E28100C42E3B0687006414B0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E28100A12E3B0683006414B0 /* Engineering2 */;
			targetProxy = E28100C32E3B0687006414B0 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		E28100CC2E3B0687006414B0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Engineering2/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.Engineering2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E28100CD2E3B0687006414B0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Engineering2/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.Engineering2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		E28100CE2E3B0687006414B0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E28100CF2E3B0687006414B0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E28100D12E3B0687006414B0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.Engineering2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Engineering2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Engineering2";
			};
			name = Debug;
		};
		E28100D22E3B0687006414B0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.Engineering2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Engineering2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Engineering2";
			};
			name = Release;
		};
		E28100D42E3B0687006414B0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.Engineering2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Engineering2;
			};
			name = Debug;
		};
		E28100D52E3B0687006414B0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.Engineering2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Engineering2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E281009D2E3B0683006414B0 /* Build configuration list for PBXProject "Engineering2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E28100CE2E3B0687006414B0 /* Debug */,
				E28100CF2E3B0687006414B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E28100CB2E3B0687006414B0 /* Build configuration list for PBXNativeTarget "Engineering2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E28100CC2E3B0687006414B0 /* Debug */,
				E28100CD2E3B0687006414B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E28100D02E3B0687006414B0 /* Build configuration list for PBXNativeTarget "Engineering2Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E28100D12E3B0687006414B0 /* Debug */,
				E28100D22E3B0687006414B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E28100D32E3B0687006414B0 /* Build configuration list for PBXNativeTarget "Engineering2UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E28100D42E3B0687006414B0 /* Debug */,
				E28100D52E3B0687006414B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E281009A2E3B0683006414B0 /* Project object */;
}
